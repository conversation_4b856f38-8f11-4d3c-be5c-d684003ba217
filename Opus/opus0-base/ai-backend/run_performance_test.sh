#!/bin/bash
# run_performance_test.sh
# Convenience script to run the LinkAnalysisAgent performance comparison test

set -e

echo "🔬 LinkAnalysisAgent Performance Test Runner"
echo "============================================="

# Check if we're in the right directory
if [ ! -f "integration_test_link_analysis_performance.py" ]; then
    echo "❌ Error: integration_test_link_analysis_performance.py not found"
    echo "   Please run this script from the ai-backend directory"
    exit 1
fi

# Check if both agent files exist
if [ ! -f "app/agents/workers/link_analysis_agent.py" ]; then
    echo "❌ Error: Original LinkAnalysisAgent not found"
    echo "   Expected: app/agents/workers/link_analysis_agent.py"
    exit 1
fi

if [ ! -f "app/agents/workers/new_link_analysis_agent.py" ]; then
    echo "❌ Error: Optimized LinkAnalysisAgent not found"
    echo "   Expected: app/agents/workers/new_link_analysis_agent.py"
    exit 1
fi

echo "✅ Both agent implementations found"
echo ""

# Parse command line arguments
QUICK_MODE=false
NO_CONFIRM=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --quick)
            QUICK_MODE=true
            shift
            ;;
        --no-confirm)
            NO_CONFIRM=true
            shift
            ;;
        --help|-h)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --quick      Run quick test with subset of scenarios"
            echo "  --no-confirm Skip confirmation prompt (for automated testing)"
            echo "  --help, -h   Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                    # Run full test suite"
            echo "  $0 --quick           # Run quick test"
            echo "  $0 --no-confirm      # Run without prompts"
            echo "  $0 --quick --no-confirm  # Quick automated test"
            exit 0
            ;;
        *)
            echo "❌ Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Build command
CMD="python3 integration_test_link_analysis_performance.py"

if [ "$QUICK_MODE" = true ]; then
    CMD="$CMD --quick"
    echo "🚀 Running QUICK performance test..."
else
    echo "🔬 Running FULL performance test..."
fi

if [ "$NO_CONFIRM" = true ]; then
    CMD="$CMD --no-confirm"
    echo "   (Skipping confirmation prompts)"
fi

echo ""
echo "Command: $CMD"
echo ""

# Check Python dependencies
echo "🔍 Checking Python dependencies..."

# Check if required modules are available
python3 -c "
import sys
missing = []

try:
    import asyncio
    import httpx
    import yt_dlp
    import youtube_transcript_api
    import langchain_google_genai
except ImportError as e:
    missing.append(str(e))

if missing:
    print('❌ Missing dependencies:')
    for m in missing:
        print(f'   {m}')
    print('')
    print('Please install missing dependencies and try again.')
    sys.exit(1)
else:
    print('✅ All required dependencies found')
" || exit 1

echo ""

# Run the test
echo "🏃 Starting performance test..."
echo "⏱️  This may take several minutes depending on network conditions..."
echo ""

# Execute the test
if eval $CMD; then
    echo ""
    echo "🎉 Performance test completed successfully!"
    echo "📄 Check LINK_ANALYSIS_PERFORMANCE_REPORT.md for detailed results"
    exit 0
else
    echo ""
    echo "❌ Performance test failed"
    echo "📋 Check the output above for error details"
    exit 1
fi

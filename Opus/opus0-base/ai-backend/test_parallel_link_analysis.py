#!/usr/bin/env python3
# test_parallel_link_analysis.py
"""
Test script to verify the parallel processing improvements in LinkAnalysisAgent.
"""

import sys
import asyncio
import time
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_import():
    """Test that the agent can be imported without errors."""
    try:
        from app.agents.workers.link_analysis_agent import LinkAnalysisAgent
        print("✅ Import successful")
        return True
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def test_initialization():
    """Test that the agent can be initialized properly."""
    try:
        from app.agents.workers.link_analysis_agent import LinkAnalysisAgent
        agent = LinkAnalysisAgent("test_agent", "test_chat")
        print("✅ Initialization successful")
        print(f"   Agent ID: {agent.agent_id}")
        print(f"   Agent Type: {agent.agent_type}")
        print(f"   Chat ID: {agent.chat_id}")
        return True
    except Exception as e:
        print(f"❌ Initialization failed: {e}")
        return False

def test_youtube_detection():
    """Test YouTube URL detection logic."""
    try:
        from app.agents.workers.link_analysis_agent import LinkAnalysisAgent
        
        test_cases = [
            ("https://www.youtube.com/watch?v=dQw4w9WgXcQ", True),
            ("https://youtu.be/dQw4w9WgXcQ", True),
            ("https://m.youtube.com/watch?v=dQw4w9WgXcQ", False),  # Not currently supported
            ("https://www.google.com", False),
            ("https://example.com", False),
        ]
        
        for url, expected in test_cases:
            result = LinkAnalysisAgent._is_youtube(url)
            status = "✅" if result == expected else "❌"
            print(f"   {status} {url} -> {result} (expected {expected})")
        
        print("✅ YouTube detection test completed")
        return True
    except Exception as e:
        print(f"❌ YouTube detection test failed: {e}")
        return False

def test_url_extraction():
    """Test URL extraction logic."""
    try:
        from app.agents.workers.link_analysis_agent import LinkAnalysisAgent
        
        test_cases = [
            ("Check out this video: https://www.youtube.com/watch?v=dQw4w9WgXcQ", 
             "https://www.youtube.com/watch?v=dQw4w9WgXcQ"),
            ("Visit https://example.com for more info", "https://example.com"),
            ("No URL in this text", None),
            ("Multiple URLs: https://first.com and https://second.com", "https://first.com"),
        ]
        
        for text, expected in test_cases:
            result = LinkAnalysisAgent._extract_link(text)
            status = "✅" if result == expected else "❌"
            print(f"   {status} '{text[:30]}...' -> {result}")
        
        print("✅ URL extraction test completed")
        return True
    except Exception as e:
        print(f"❌ URL extraction test failed: {e}")
        return False

def test_json_parsing():
    """Test JSON parsing logic for LLM outputs."""
    try:
        from app.agents.workers.link_analysis_agent import LinkAnalysisAgent
        
        test_cases = [
            ('["https://example.com"]', "https://example.com"),
            ('```json\n["https://example.com"]\n```', "https://example.com"),
            ('```\n["https://example.com"]\n```', "https://example.com"),
            ('["invalid-url"]', None),  # Should return None for non-http URLs
            ('[]', None),
            ('invalid json', None),
        ]
        
        for json_str, expected in test_cases:
            result = LinkAnalysisAgent._parse_link_json(json_str)
            status = "✅" if result == expected else "❌"
            print(f"   {status} '{json_str[:20]}...' -> {result}")
        
        print("✅ JSON parsing test completed")
        return True
    except Exception as e:
        print(f"❌ JSON parsing test failed: {e}")
        return False

async def test_http_client_management():
    """Test HTTP client creation and cleanup."""
    try:
        from app.agents.workers.link_analysis_agent import LinkAnalysisAgent
        
        agent = LinkAnalysisAgent("test_agent", "test_chat")
        
        # Test client creation
        client1 = await agent._get_http_client()
        client2 = await agent._get_http_client()
        
        # Should return the same client instance
        assert client1 is client2, "Should reuse the same client instance"
        print("✅ HTTP client reuse working")
        
        # Test cleanup
        await agent._close_http_client()
        print("✅ HTTP client cleanup working")
        
        # Test recreation after cleanup
        client3 = await agent._get_http_client()
        assert client3 is not client1, "Should create new client after cleanup"
        print("✅ HTTP client recreation working")
        
        await agent._close_http_client()
        print("✅ HTTP client management test completed")
        return True
    except Exception as e:
        print(f"❌ HTTP client management test failed: {e}")
        return False

def test_performance_comparison():
    """Compare performance characteristics of parallel vs sequential processing."""
    print("📊 Performance Comparison Analysis")
    print("=" * 50)
    
    # Simulate timing for different scenarios
    scenarios = [
        ("Fast YouTube video", {"metadata": 2.0, "transcript": 3.0}),
        ("Typical YouTube video", {"metadata": 2.5, "transcript": 4.0}),
        ("Slow YouTube video", {"metadata": 3.0, "transcript": 5.0}),
    ]
    
    for name, timings in scenarios:
        sequential_time = timings["metadata"] + timings["transcript"]
        parallel_time = max(timings["metadata"], timings["transcript"])
        improvement = ((sequential_time - parallel_time) / sequential_time) * 100
        
        print(f"\n{name}:")
        print(f"  Sequential: {sequential_time:.1f}s")
        print(f"  Parallel:   {parallel_time:.1f}s")
        print(f"  Improvement: {improvement:.1f}% faster")
    
    print("\n✅ Performance analysis completed")
    return True

def main():
    """Run all tests."""
    print("🚀 Testing Parallel LinkAnalysisAgent Implementation")
    print("=" * 60)
    
    tests = [
        ("Import Test", test_import),
        ("Initialization Test", test_initialization),
        ("YouTube Detection Test", test_youtube_detection),
        ("URL Extraction Test", test_url_extraction),
        ("JSON Parsing Test", test_json_parsing),
        ("Performance Analysis", test_performance_comparison),
    ]
    
    async_tests = [
        ("HTTP Client Management Test", test_http_client_management),
    ]
    
    passed = 0
    total = len(tests) + len(async_tests)
    
    # Run synchronous tests
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        print("-" * 40)
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
    
    # Run asynchronous tests
    async def run_async_tests():
        nonlocal passed
        for test_name, test_func in async_tests:
            print(f"\n🧪 {test_name}")
            print("-" * 40)
            try:
                if await test_func():
                    passed += 1
            except Exception as e:
                print(f"❌ {test_name} failed with exception: {e}")
    
    # Run async tests
    try:
        asyncio.run(run_async_tests())
    except Exception as e:
        print(f"❌ Async tests failed: {e}")
    
    # Summary
    print(f"\n📋 Test Summary")
    print("=" * 60)
    print(f"Passed: {passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 All tests passed! The parallel implementation is ready.")
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

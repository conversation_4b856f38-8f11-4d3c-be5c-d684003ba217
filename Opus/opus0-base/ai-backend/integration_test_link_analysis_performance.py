#!/usr/bin/env python3
# integration_test_link_analysis_performance.py
"""
Comprehensive Integration Test: LinkAnalysisAgent Performance Comparison

This test compares the performance of two LinkAnalysisAgent implementations:
1. Original (sequential processing)
2. Optimized (parallel processing)

The test measures end-to-end performance from an end-user perspective,
including execution time, memory usage, and success rates.
"""

import sys
import asyncio
import time
import tracemalloc
import traceback
import statistics
import json
import uuid
from pathlib import Path
from typing import Dict, Any, List, Tuple, Optional
from dataclasses import dataclass, asdict
from datetime import datetime, timezone

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

@dataclass
class TestResult:
    """Container for individual test results."""
    url: str
    url_type: str
    implementation: str
    success: bool
    execution_time: float
    memory_peak: float
    memory_current: float
    error_message: Optional[str] = None
    file_size: Optional[int] = None
    content_length: Optional[int] = None

@dataclass
class TestSummary:
    """Container for aggregated test results."""
    implementation: str
    total_tests: int
    successful_tests: int
    failed_tests: int
    success_rate: float
    avg_execution_time: float
    median_execution_time: float
    min_execution_time: float
    max_execution_time: float
    avg_memory_peak: float
    total_content_length: int

class LinkAnalysisPerformanceTest:
    """
    Comprehensive performance test for LinkAnalysisAgent implementations.
    """
    
    def __init__(self):
        self.test_results: List[TestResult] = []
        self.test_scenarios = self._define_test_scenarios()
        
    def _define_test_scenarios(self) -> List[Dict[str, Any]]:
        """Define comprehensive test scenarios covering various use cases."""
        return [
            # YouTube Videos - Short (2-4 minutes)
            {
                "url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
                "type": "youtube_short",
                "description": "Rick Astley - Never Gonna Give You Up (3:33)"
            },
            {
                "url": "https://youtu.be/jNQXAC9IVRw",
                "type": "youtube_short", 
                "description": "Me at the zoo (0:19)"
            },
            
            # YouTube Videos - Medium (5-10 minutes)
            {
                "url": "https://www.youtube.com/watch?v=9bZkp7q19f0",
                "type": "youtube_medium",
                "description": "PSY - GANGNAM STYLE (4:12)"
            },
            {
                "url": "https://www.youtube.com/watch?v=kqtD5dpn9C8",
                "type": "youtube_medium",
                "description": "Python Tutorial (8:45)"
            },
            
            # YouTube Videos - Long (15+ minutes)
            {
                "url": "https://www.youtube.com/watch?v=aircAruvnKk",
                "type": "youtube_long",
                "description": "Neural Networks Explained (19:13)"
            },
            
            # Generic Webpages - News Articles
            {
                "url": "https://www.bbc.com/news",
                "type": "webpage_news",
                "description": "BBC News Homepage"
            },
            {
                "url": "https://techcrunch.com",
                "type": "webpage_tech",
                "description": "TechCrunch Homepage"
            },
            
            # Generic Webpages - Documentation
            {
                "url": "https://docs.python.org/3/tutorial/",
                "type": "webpage_docs",
                "description": "Python Tutorial Documentation"
            },
            {
                "url": "https://httpx.readthedocs.io/en/latest/",
                "type": "webpage_docs",
                "description": "HTTPX Documentation"
            },
            
            # Edge Cases
            {
                "url": "https://example.com",
                "type": "webpage_simple",
                "description": "Simple Example Page"
            },
            
            # Error Cases (for error handling comparison)
            {
                "url": "https://nonexistent-domain-12345.com",
                "type": "error_case",
                "description": "Non-existent domain"
            },
            {
                "url": "https://httpstat.us/500",
                "type": "error_case", 
                "description": "HTTP 500 error"
            }
        ]

    async def run_comprehensive_test(self) -> Dict[str, Any]:
        """
        Run comprehensive performance comparison test.
        
        Returns:
            Dictionary containing detailed test results and analysis
        """
        print("🚀 Starting Comprehensive LinkAnalysisAgent Performance Test")
        print("=" * 80)
        
        # Test both implementations
        implementations = [
            ("original", "app.agents.workers.link_analysis_agent", "LinkAnalysisAgent"),
            ("optimized", "app.agents.workers.new_link_analysis_agent", "LinkAnalysisAgent")
        ]
        
        all_results = []
        
        for impl_name, module_path, class_name in implementations:
            print(f"\n📊 Testing {impl_name.upper()} implementation")
            print("-" * 50)
            
            results = await self._test_implementation(impl_name, module_path, class_name)
            all_results.extend(results)
            
            # Brief summary after each implementation
            success_count = sum(1 for r in results if r.success)
            avg_time = statistics.mean([r.execution_time for r in results if r.success])
            print(f"✅ {success_count}/{len(results)} tests passed")
            print(f"⏱️  Average execution time: {avg_time:.2f}s")
        
        # Generate comprehensive analysis
        analysis = self._analyze_results(all_results)
        
        # Generate reports
        console_report = self._generate_console_report(analysis)
        markdown_report = self._generate_markdown_report(analysis)
        
        print(console_report)
        
        # Save markdown report
        report_path = project_root / "LINK_ANALYSIS_PERFORMANCE_REPORT.md"
        with open(report_path, "w", encoding="utf-8") as f:
            f.write(markdown_report)
        
        print(f"\n📄 Detailed report saved to: {report_path}")
        
        return analysis

    async def _test_implementation(
        self, 
        impl_name: str, 
        module_path: str, 
        class_name: str
    ) -> List[TestResult]:
        """Test a specific implementation with all scenarios."""
        results = []
        
        try:
            # Dynamic import of the implementation
            module = __import__(module_path, fromlist=[class_name])
            AgentClass = getattr(module, class_name)
        except ImportError as e:
            print(f"❌ Failed to import {impl_name} implementation: {e}")
            return []
        
        for i, scenario in enumerate(self.test_scenarios, 1):
            print(f"  Test {i}/{len(self.test_scenarios)}: {scenario['description']}")
            
            result = await self._run_single_test(
                AgentClass, 
                impl_name, 
                scenario
            )
            results.append(result)
            
            # Brief result indicator
            status = "✅" if result.success else "❌"
            time_str = f"{result.execution_time:.2f}s" if result.success else "FAILED"
            print(f"    {status} {time_str}")
            
            # Small delay between tests to avoid rate limiting
            await asyncio.sleep(1)
        
        return results

    async def _run_single_test(
        self, 
        AgentClass, 
        impl_name: str, 
        scenario: Dict[str, Any]
    ) -> TestResult:
        """Run a single test scenario and measure performance."""
        
        # Generate unique identifiers for this test
        chat_id = f"test_chat_{uuid.uuid4().hex[:8]}"
        agent_id = f"test_agent_{uuid.uuid4().hex[:8]}"
        subtask_id = f"test_subtask_{uuid.uuid4().hex[:8]}"
        
        # Prepare task details
        task_details = {
            "subtask_id": subtask_id,
            "subtask_description": f"Analyze this link: {scenario['url']}",
            "deps_info": "",
            "user_message": f"Please analyze this link: {scenario['url']}"
        }
        
        # Start memory tracking
        tracemalloc.start()
        
        try:
            # Create agent instance
            agent = AgentClass(agent_id, chat_id)
            
            # Measure execution time
            start_time = time.perf_counter()
            
            # Execute the task
            await agent.execute_task(task_details)
            
            end_time = time.perf_counter()
            execution_time = end_time - start_time
            
            # Get memory usage
            current, peak = tracemalloc.get_traced_memory()
            tracemalloc.stop()
            
            # Check if output file was created and get its size
            file_size = None
            content_length = None
            
            # Try to find the generated file (this is implementation-specific)
            # We'll look for files in the knowledge base directory
            try:
                from app.utils.constants import get_knowledge_base_dir
                kb_dir = get_knowledge_base_dir(chat_id)
                if kb_dir.exists():
                    md_files = list(kb_dir.glob(f"link_analysis_{subtask_id}_*.md"))
                    if md_files:
                        file_path = md_files[0]
                        file_size = file_path.stat().st_size
                        content_length = len(file_path.read_text(encoding="utf-8"))
            except Exception:
                pass  # File size measurement is optional
            
            return TestResult(
                url=scenario["url"],
                url_type=scenario["type"],
                implementation=impl_name,
                success=True,
                execution_time=execution_time,
                memory_peak=peak / 1024 / 1024,  # Convert to MB
                memory_current=current / 1024 / 1024,  # Convert to MB
                file_size=file_size,
                content_length=content_length
            )
            
        except Exception as e:
            tracemalloc.stop()
            
            return TestResult(
                url=scenario["url"],
                url_type=scenario["type"],
                implementation=impl_name,
                success=False,
                execution_time=0.0,
                memory_peak=0.0,
                memory_current=0.0,
                error_message=str(e)
            )
        
        finally:
            # Cleanup: remove any created files/directories
            try:
                from app.utils.constants import get_knowledge_base_dir
                kb_dir = get_knowledge_base_dir(chat_id)
                if kb_dir.exists():
                    import shutil
                    shutil.rmtree(kb_dir, ignore_errors=True)
            except Exception:
                pass  # Cleanup is best-effort

    def _analyze_results(self, results: List[TestResult]) -> Dict[str, Any]:
        """Analyze test results and generate comprehensive statistics."""

        # Group results by implementation
        original_results = [r for r in results if r.implementation == "original"]
        optimized_results = [r for r in results if r.implementation == "optimized"]

        # Generate summaries for each implementation
        original_summary = self._generate_summary(original_results, "original")
        optimized_summary = self._generate_summary(optimized_results, "optimized")

        # Calculate performance improvements
        improvements = self._calculate_improvements(original_summary, optimized_summary)

        # Group by URL type for detailed analysis
        url_type_analysis = self._analyze_by_url_type(results)

        return {
            "test_timestamp": datetime.now(timezone.utc).isoformat(),
            "total_scenarios": len(self.test_scenarios),
            "original_summary": asdict(original_summary),
            "optimized_summary": asdict(optimized_summary),
            "improvements": improvements,
            "url_type_analysis": url_type_analysis,
            "detailed_results": [asdict(r) for r in results],
            "statistical_significance": self._calculate_statistical_significance(results)
        }

    def _generate_summary(self, results: List[TestResult], impl_name: str) -> TestSummary:
        """Generate summary statistics for an implementation."""

        successful_results = [r for r in results if r.success]

        if not successful_results:
            return TestSummary(
                implementation=impl_name,
                total_tests=len(results),
                successful_tests=0,
                failed_tests=len(results),
                success_rate=0.0,
                avg_execution_time=0.0,
                median_execution_time=0.0,
                min_execution_time=0.0,
                max_execution_time=0.0,
                avg_memory_peak=0.0,
                total_content_length=0
            )

        execution_times = [r.execution_time for r in successful_results]
        memory_peaks = [r.memory_peak for r in successful_results]
        content_lengths = [r.content_length for r in successful_results if r.content_length]

        return TestSummary(
            implementation=impl_name,
            total_tests=len(results),
            successful_tests=len(successful_results),
            failed_tests=len(results) - len(successful_results),
            success_rate=len(successful_results) / len(results) * 100,
            avg_execution_time=statistics.mean(execution_times),
            median_execution_time=statistics.median(execution_times),
            min_execution_time=min(execution_times),
            max_execution_time=max(execution_times),
            avg_memory_peak=statistics.mean(memory_peaks),
            total_content_length=sum(content_lengths)
        )

    def _calculate_improvements(self, original: TestSummary, optimized: TestSummary) -> Dict[str, Any]:
        """Calculate performance improvements between implementations."""

        if original.avg_execution_time == 0:
            return {"error": "Cannot calculate improvements - original implementation failed"}

        time_improvement = ((original.avg_execution_time - optimized.avg_execution_time)
                           / original.avg_execution_time * 100)

        memory_improvement = ((original.avg_memory_peak - optimized.avg_memory_peak)
                             / original.avg_memory_peak * 100) if original.avg_memory_peak > 0 else 0

        success_rate_change = optimized.success_rate - original.success_rate

        return {
            "execution_time_improvement_percent": time_improvement,
            "memory_improvement_percent": memory_improvement,
            "success_rate_change_percent": success_rate_change,
            "absolute_time_savings_seconds": original.avg_execution_time - optimized.avg_execution_time,
            "median_time_improvement_percent": ((original.median_execution_time - optimized.median_execution_time)
                                               / original.median_execution_time * 100) if original.median_execution_time > 0 else 0
        }

    def _analyze_by_url_type(self, results: List[TestResult]) -> Dict[str, Any]:
        """Analyze performance by URL type (YouTube vs generic webpages)."""

        analysis = {}

        # Group by URL type
        url_types = set(r.url_type for r in results)

        for url_type in url_types:
            type_results = [r for r in results if r.url_type == url_type]
            original_type = [r for r in type_results if r.implementation == "original" and r.success]
            optimized_type = [r for r in type_results if r.implementation == "optimized" and r.success]

            if original_type and optimized_type:
                orig_avg = statistics.mean([r.execution_time for r in original_type])
                opt_avg = statistics.mean([r.execution_time for r in optimized_type])
                improvement = ((orig_avg - opt_avg) / orig_avg * 100) if orig_avg > 0 else 0

                analysis[url_type] = {
                    "original_avg_time": orig_avg,
                    "optimized_avg_time": opt_avg,
                    "improvement_percent": improvement,
                    "sample_size": len(original_type)
                }

        return analysis

    def _calculate_statistical_significance(self, results: List[TestResult]) -> Dict[str, Any]:
        """Calculate statistical significance of performance differences."""

        original_times = [r.execution_time for r in results
                         if r.implementation == "original" and r.success]
        optimized_times = [r.execution_time for r in results
                          if r.implementation == "optimized" and r.success]

        if len(original_times) < 3 or len(optimized_times) < 3:
            return {"error": "Insufficient data for statistical analysis"}

        # Basic statistical measures
        original_std = statistics.stdev(original_times)
        optimized_std = statistics.stdev(optimized_times)

        # Effect size (Cohen's d)
        pooled_std = ((original_std ** 2 + optimized_std ** 2) / 2) ** 0.5
        cohens_d = (statistics.mean(original_times) - statistics.mean(optimized_times)) / pooled_std if pooled_std > 0 else 0

        return {
            "original_std_dev": original_std,
            "optimized_std_dev": optimized_std,
            "cohens_d_effect_size": cohens_d,
            "effect_size_interpretation": self._interpret_effect_size(cohens_d),
            "sample_sizes": {
                "original": len(original_times),
                "optimized": len(optimized_times)
            }
        }

    def _interpret_effect_size(self, cohens_d: float) -> str:
        """Interpret Cohen's d effect size."""
        abs_d = abs(cohens_d)
        if abs_d < 0.2:
            return "negligible"
        elif abs_d < 0.5:
            return "small"
        elif abs_d < 0.8:
            return "medium"
        else:
            return "large"

    def _generate_console_report(self, analysis: Dict[str, Any]) -> str:
        """Generate a concise console report."""

        original = analysis["original_summary"]
        optimized = analysis["optimized_summary"]
        improvements = analysis["improvements"]

        report = [
            "\n" + "=" * 80,
            "🎯 PERFORMANCE COMPARISON RESULTS",
            "=" * 80,
            "",
            f"📊 Test Summary:",
            f"   Total Scenarios: {analysis['total_scenarios']}",
            f"   Test Timestamp: {analysis['test_timestamp']}",
            "",
            f"🔄 Original Implementation:",
            f"   Success Rate: {original['success_rate']:.1f}% ({original['successful_tests']}/{original['total_tests']})",
            f"   Average Time: {original['avg_execution_time']:.2f}s",
            f"   Median Time:  {original['median_execution_time']:.2f}s",
            f"   Memory Peak:  {original['avg_memory_peak']:.1f} MB",
            "",
            f"⚡ Optimized Implementation:",
            f"   Success Rate: {optimized['success_rate']:.1f}% ({optimized['successful_tests']}/{optimized['total_tests']})",
            f"   Average Time: {optimized['avg_execution_time']:.2f}s",
            f"   Median Time:  {optimized['median_execution_time']:.2f}s",
            f"   Memory Peak:  {optimized['avg_memory_peak']:.1f} MB",
            "",
            f"🚀 Performance Improvements:",
            f"   Execution Time: {improvements['execution_time_improvement_percent']:.1f}% faster",
            f"   Time Savings:  {improvements['absolute_time_savings_seconds']:.2f}s per request",
            f"   Memory Usage:  {improvements['memory_improvement_percent']:.1f}% {'better' if improvements['memory_improvement_percent'] > 0 else 'worse'}",
            f"   Success Rate:  {improvements['success_rate_change_percent']:.1f}% change",
            "",
            f"📈 Statistical Analysis:",
            f"   Effect Size: {analysis['statistical_significance'].get('cohens_d_effect_size', 'N/A'):.2f} ({analysis['statistical_significance'].get('effect_size_interpretation', 'N/A')})",
            "",
            f"🎯 URL Type Breakdown:"
        ]

        # Add URL type analysis
        for url_type, data in analysis["url_type_analysis"].items():
            report.append(f"   {url_type}: {data['improvement_percent']:.1f}% improvement ({data['sample_size']} samples)")

        report.extend([
            "",
            "=" * 80,
            ""
        ])

        return "\n".join(report)

    def _generate_markdown_report(self, analysis: Dict[str, Any]) -> str:
        """Generate a detailed markdown report."""

        original = analysis["original_summary"]
        optimized = analysis["optimized_summary"]
        improvements = analysis["improvements"]

        report = [
            "# LinkAnalysisAgent Performance Comparison Report",
            "",
            f"**Generated:** {analysis['test_timestamp']}",
            f"**Total Test Scenarios:** {analysis['total_scenarios']}",
            "",
            "## Executive Summary",
            "",
            f"The optimized LinkAnalysisAgent implementation shows **{improvements['execution_time_improvement_percent']:.1f}% improvement** in execution time compared to the original implementation, with an average time savings of **{improvements['absolute_time_savings_seconds']:.2f} seconds** per request.",
            "",
            "## Performance Metrics Comparison",
            "",
            "| Metric | Original | Optimized | Improvement |",
            "|--------|----------|-----------|-------------|",
            f"| Success Rate | {original['success_rate']:.1f}% | {optimized['success_rate']:.1f}% | {improvements['success_rate_change_percent']:.1f}% |",
            f"| Average Execution Time | {original['avg_execution_time']:.2f}s | {optimized['avg_execution_time']:.2f}s | {improvements['execution_time_improvement_percent']:.1f}% |",
            f"| Median Execution Time | {original['median_execution_time']:.2f}s | {optimized['median_execution_time']:.2f}s | {improvements['median_time_improvement_percent']:.1f}% |",
            f"| Min Execution Time | {original['min_execution_time']:.2f}s | {optimized['min_execution_time']:.2f}s | - |",
            f"| Max Execution Time | {original['max_execution_time']:.2f}s | {optimized['max_execution_time']:.2f}s | - |",
            f"| Average Memory Peak | {original['avg_memory_peak']:.1f} MB | {optimized['avg_memory_peak']:.1f} MB | {improvements['memory_improvement_percent']:.1f}% |",
            "",
            "## Performance by URL Type",
            "",
            "| URL Type | Original Avg Time | Optimized Avg Time | Improvement | Sample Size |",
            "|----------|-------------------|-------------------|-------------|-------------|"
        ]

        # Add URL type breakdown
        for url_type, data in analysis["url_type_analysis"].items():
            report.append(f"| {url_type} | {data['original_avg_time']:.2f}s | {data['optimized_avg_time']:.2f}s | {data['improvement_percent']:.1f}% | {data['sample_size']} |")

        report.extend([
            "",
            "## Statistical Analysis",
            "",
            f"- **Effect Size (Cohen's d):** {analysis['statistical_significance'].get('cohens_d_effect_size', 'N/A'):.3f}",
            f"- **Effect Size Interpretation:** {analysis['statistical_significance'].get('effect_size_interpretation', 'N/A').title()}",
            f"- **Original Standard Deviation:** {analysis['statistical_significance'].get('original_std_dev', 'N/A'):.3f}s",
            f"- **Optimized Standard Deviation:** {analysis['statistical_significance'].get('optimized_std_dev', 'N/A'):.3f}s",
            "",
            "## Detailed Test Results",
            "",
            "| URL | Type | Implementation | Success | Time (s) | Memory (MB) | Error |",
            "|-----|------|----------------|---------|----------|-------------|-------|"
        ])

        # Add detailed results
        for result in analysis["detailed_results"]:
            success_icon = "✅" if result["success"] else "❌"
            time_str = f"{result['execution_time']:.2f}" if result["success"] else "N/A"
            memory_str = f"{result['memory_peak']:.1f}" if result["success"] else "N/A"
            error_str = result.get("error_message", "")[:50] + "..." if result.get("error_message") and len(result.get("error_message", "")) > 50 else result.get("error_message", "")

            report.append(f"| {result['url'][:50]}... | {result['url_type']} | {result['implementation']} | {success_icon} | {time_str} | {memory_str} | {error_str} |")

        report.extend([
            "",
            "## Conclusions",
            "",
            "### Key Findings",
            "",
            f"1. **Performance Improvement:** The optimized implementation is {improvements['execution_time_improvement_percent']:.1f}% faster on average",
            f"2. **Consistency:** {'Improved' if improvements['execution_time_improvement_percent'] > 0 else 'Reduced'} execution time consistency",
            f"3. **Reliability:** {'Improved' if improvements['success_rate_change_percent'] >= 0 else 'Reduced'} success rate",
            f"4. **Memory Efficiency:** {'Improved' if improvements['memory_improvement_percent'] > 0 else 'Similar'} memory usage",
            "",
            "### Recommendations",
            "",
        ])

        # Add recommendations based on results
        if improvements['execution_time_improvement_percent'] > 20:
            report.append("- ✅ **Deploy optimized implementation** - Significant performance improvement detected")
        elif improvements['execution_time_improvement_percent'] > 10:
            report.append("- ⚠️ **Consider deployment** - Moderate performance improvement detected")
        else:
            report.append("- 🔍 **Further analysis needed** - Minimal performance improvement detected")

        if improvements['success_rate_change_percent'] < 0:
            report.append("- ⚠️ **Investigate reliability issues** - Success rate decreased in optimized version")

        report.extend([
            "",
            "---",
            "",
            f"*Report generated by LinkAnalysisAgent Performance Test Suite on {analysis['test_timestamp']}*"
        ])

        return "\n".join(report)


async def main():
    """Main execution function."""
    print("🔬 LinkAnalysisAgent Performance Comparison Test")
    print("=" * 60)
    print("This test will compare the performance of two implementations:")
    print("1. Original (sequential processing)")
    print("2. Optimized (parallel processing)")
    print("")

    # Check if both implementations are available
    try:
        from app.agents.workers.link_analysis_agent import LinkAnalysisAgent as OriginalAgent
        print(f"✅ Original implementation found: {OriginalAgent.__name__}")
    except ImportError as e:
        print(f"❌ Original implementation not found: {e}")
        return False

    try:
        from app.agents.workers.new_link_analysis_agent import LinkAnalysisAgent as OptimizedAgent
        print(f"✅ Optimized implementation found: {OptimizedAgent.__name__}")
    except ImportError as e:
        print(f"❌ Optimized implementation not found: {e}")
        return False

    print("")
    print("⚠️  WARNING: This test will make real HTTP requests to external services.")
    print("   It may take several minutes to complete and could be affected by:")
    print("   - Network latency and availability")
    print("   - Rate limiting from external APIs")
    print("   - YouTube content availability")
    print("")

    # Ask for confirmation
    try:
        response = input("Do you want to proceed? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("Test cancelled.")
            return False
    except KeyboardInterrupt:
        print("\nTest cancelled.")
        return False

    # Run the comprehensive test
    test_suite = LinkAnalysisPerformanceTest()

    try:
        results = await test_suite.run_comprehensive_test()

        # Determine overall success
        improvements = results["improvements"]
        success_rate_maintained = improvements["success_rate_change_percent"] >= -5  # Allow 5% degradation
        performance_improved = improvements["execution_time_improvement_percent"] > 0

        overall_success = success_rate_maintained and performance_improved

        print(f"\n🎯 OVERALL TEST RESULT: {'✅ PASS' if overall_success else '❌ FAIL'}")

        if overall_success:
            print("   The optimized implementation shows measurable improvements!")
        else:
            print("   The optimized implementation needs further work.")

        return overall_success

    except KeyboardInterrupt:
        print("\n\n⚠️ Test interrupted by user.")
        return False
    except Exception as e:
        print(f"\n\n❌ Test failed with error: {e}")
        traceback.print_exc()
        return False


def run_quick_test():
    """Run a quick test with a subset of scenarios for faster feedback."""
    print("🚀 Quick Performance Test (subset of scenarios)")
    print("=" * 50)

    # Create a test instance with reduced scenarios
    test_suite = LinkAnalysisPerformanceTest()

    # Use only a few representative scenarios
    test_suite.test_scenarios = [
        {
            "url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            "type": "youtube_short",
            "description": "Rick Astley - Never Gonna Give You Up (Quick Test)"
        },
        {
            "url": "https://example.com",
            "type": "webpage_simple",
            "description": "Simple Example Page (Quick Test)"
        }
    ]

    return asyncio.run(test_suite.run_comprehensive_test())


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(
        description="Performance comparison test for LinkAnalysisAgent implementations"
    )
    parser.add_argument(
        "--quick",
        action="store_true",
        help="Run quick test with subset of scenarios"
    )
    parser.add_argument(
        "--no-confirm",
        action="store_true",
        help="Skip confirmation prompt (for automated testing)"
    )

    args = parser.parse_args()

    if args.quick:
        success = run_quick_test()
    else:
        if args.no_confirm:
            # Monkey patch input for automated testing
            import builtins
            builtins.input = lambda _: "y"

        success = asyncio.run(main())

    sys.exit(0 if success else 1)
